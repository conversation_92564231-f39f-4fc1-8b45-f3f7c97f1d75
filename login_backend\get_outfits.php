<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权，请先登录'
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

// 处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();

    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20; // 默认每页20条
    $offset = ($page - 1) * $perPage;
    
    // 获取分类过滤参数
    $categoryId = isset($_GET['category_id']) ? intval($_GET['category_id']) : null;

    // 新增：圈子数据相关参数（向后兼容）
    $includeCircleData = isset($_GET['include_circle_data']) ? $_GET['include_circle_data'] === 'true' : false;
    $dataSource = isset($_GET['data_source']) ? $_GET['data_source'] : 'personal'; // personal, shared, all

    // 构建查询条件（向后兼容）
    if ($includeCircleData) {
        // 新功能：包含圈子数据的查询
        if ($dataSource === 'personal') {
            $whereClause = "WHERE o.user_id = :user_id AND o.circle_id IS NULL";
            $params = ['user_id' => $userId];
        } elseif ($dataSource === 'shared') {
            // 查询用户所在圈子的共享数据（排除用户自己的数据）
            $whereClause = "WHERE o.circle_id IS NOT NULL AND o.user_id != :user_id AND o.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')";
            $params = ['user_id' => $userId];

            // 添加调试日志
            error_log("查询共享穿搭数据（排除自己）- 用户ID: $userId");
        } else { // $dataSource === 'all'
            // 查询个人数据 + 圈子共享数据（修复：优先包含用户自己的穿搭，不管circle_id状态）
            $whereClause = "WHERE (o.user_id = :user_id) OR
                           (o.circle_id IS NOT NULL AND o.user_id != :user_id AND o.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))";
            $params = ['user_id' => $userId];

            // 添加调试日志
            error_log("查询全部穿搭数据（修复后）- 用户ID: $userId，包含用户自己的所有穿搭");
        }
    } else {
        // 原有逻辑：只查询个人数据（保持向后兼容）
        $whereClause = "WHERE o.user_id = :user_id";
        $params = ['user_id' => $userId];
    }
    
    // 如果指定了分类ID，添加分类过滤条件
    if ($categoryId) {
        $whereClause .= " AND o.category_id = :category_id";
        $params['category_id'] = $categoryId;
    }

    // 查询总数据量
    $countStmt = $conn->prepare("
        SELECT COUNT(*) 
        FROM outfits o
        LEFT JOIN outfit_categories c ON o.category_id = c.id
        $whereClause
    ");
    foreach ($params as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    $totalCount = $countStmt->fetchColumn();
    $totalPages = ceil($totalCount / $perPage);

    // 查询穿搭列表，按创建时间倒序排列
    if ($includeCircleData) {
        // 包含圈子数据时，添加创建者信息和数据源标识
        $stmt = $conn->prepare("
            SELECT o.id, o.name, o.description, o.thumbnail_url, o.outfit_data, o.category_id,
                   c.name as category_name, o.created_at, o.updated_at, o.is_public, o.user_id,
                   o.likes_count, o.circle_id, u.nickname as creator_nickname,
                   CASE WHEN o.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM outfits o
            LEFT JOIN outfit_categories c ON o.category_id = c.id
            LEFT JOIN users u ON o.user_id = u.id
            $whereClause
            ORDER BY o.created_at DESC
            LIMIT :offset, :per_page
        ");
    } else {
        // 原有查询（向后兼容）
        $stmt = $conn->prepare("
            SELECT o.id, o.name, o.description, o.thumbnail_url, o.outfit_data, o.category_id,
                   c.name as category_name, o.created_at, o.updated_at, o.is_public, o.user_id,
                   o.likes_count
            FROM outfits o
            LEFT JOIN outfit_categories c ON o.category_id = c.id
            $whereClause
            ORDER BY o.created_at DESC
            LIMIT :offset, :per_page
        ");
    }
    
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':per_page', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理查询结果
    $result = [];
    foreach ($outfits as $outfit) {
        // 解析JSON数据
        $outfitData = json_decode($outfit['outfit_data'], true);
        
        // 构建返回的穿搭对象
        $resultOutfit = [
            'id' => $outfit['id'],
            'name' => $outfit['name'],
            'description' => $outfit['description'],
            'thumbnail' => $outfit['thumbnail_url'],
            'category_id' => $outfit['category_id'],
            'category_name' => $outfit['category_name'],
            'created_at' => $outfit['created_at'],
            'updated_at' => $outfit['updated_at'],
            'items' => $outfitData['items'] ?? [],
            'is_public' => (int)$outfit['is_public'],
            'user_id' => $outfit['user_id'],
            'likes_count' => isset($outfit['likes_count']) ? (int)$outfit['likes_count'] : 0
        ];

        // 添加数据源相关字段
        if ($includeCircleData) {
            $resultOutfit['data_source'] = $outfit['data_source'];
            $resultOutfit['creator_nickname'] = $outfit['creator_nickname'];
            $resultOutfit['is_shared'] = $outfit['data_source'] === 'shared';
            $resultOutfit['is_own'] = $outfit['user_id'] == $userId;
        } else {
            // 向后兼容：为原有API添加默认值
            $resultOutfit['data_source'] = 'personal';
            $resultOutfit['creator_nickname'] = null;
            $resultOutfit['is_shared'] = false;
            $resultOutfit['is_own'] = true;
        }
        
        $result[] = $resultOutfit;
    }
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => $result,
        'pagination' => [
            'total' => $totalCount,
            'per_page' => $perPage,
            'current_page' => $page,
            'total_pages' => $totalPages
        ],
        'meta' => [
            'include_circle_data' => $includeCircleData,
            'data_source' => $dataSource,
            'total_count' => count($result)
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '获取穿搭列表失败: ' . $e->getMessage()
    ]);
} 